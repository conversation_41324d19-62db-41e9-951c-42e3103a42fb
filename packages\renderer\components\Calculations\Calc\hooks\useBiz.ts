import { useBizEngine, useBizMain, useHandler } from '@/renderer/hooks'
import { CalcMode, calcModefnMap, CalcModeType } from '../constants'
import { BizEngine, BizMain } from '@/renderer/logic'
import { CalcGroupable, CalsGroupItem, CfgFileType } from '@wuk/cfg'

/**
 * @description 决定接口调用的模块 main | engine
 */
export const useBiz = (mode: CalcModeType) => {
  const bizEngine = useBizEngine()
  const bizMain = useBizMain()
  const dynamicApi = () => {
    if (mode === CalcMode.Sys_common) {
      return {
        baseFn: () => {
          return Promise.resolve(true)
        },
        copyCommonToSpecific: () => {
          return Promise.resolve(true)
        },
        pasteSpecificToCommon: () => {
          return Promise.resolve(true)
        },
        readCalcs: bizMain.value?.readCalcsOptions.bind(bizMain.value),
        modifyCalcs: bizMain.value?.modifyCalcFile.bind(bizMain.value),
        removeCalcs: bizMain.value?.removeCalcFile.bind(bizMain.value),
        addCalcs: bizMain.value?.addCalcFile.bind(bizMain.value),
        loadCalcText: bizMain.value?.loadCalcText.bind(bizEngine.value),
        saveCalcText: bizMain.value?.saveCalcText.bind(bizEngine.value),
        loadCalc: bizMain.value?.loadCalc.bind(bizEngine.value), // group item加载计算
        modifyCalc: bizMain.value?.modifyCalc.bind(bizEngine.value), // group item修改计算
        saveCalc: bizMain.value?.saveCalc.bind(bizEngine.value), // group item保存计算
        bindHandler: (handler: (...args: any[]) => void) => {
          useHandler(bizMain, BizMain.onCalcsOptionsChanged, handler)
        }
      }
    } else {
      const fnPtr = calcModefnMap[mode]
      const engineApi = {
        readCalcs: bizEngine.value?.[fnPtr.readFnName].bind(bizEngine.value), // group 读取计算
        modifyCalcs: bizEngine.value?.[fnPtr.modifyFnName].bind(bizEngine.value), // group 修改计算
        removeCalcs: bizEngine.value?.[fnPtr.removeFnName].bind(bizEngine.value), // group 删除计算
        addCalcs: bizEngine.value?.[fnPtr.addFnName].bind(bizEngine.value), // group 新增计算
        loadCalcText: bizEngine.value?.loadCalcText.bind(bizEngine.value),
        saveCalcText: bizEngine.value?.saveCalcText.bind(bizEngine.value),
        loadCalc: bizEngine.value?.loadCalc.bind(bizEngine.value), // group item加载计算
        modifyCalc: bizEngine.value?.modifyCalc.bind(bizEngine.value), // group item修改计算
        saveCalc: bizEngine.value?.saveCalc.bind(bizEngine.value) // group item保存计算
      }
      const dragCalcApi = {
        baseFn: async (
          groupItem: CalsGroupItem,
          groupable: Partial<CalcGroupable>,
          type: CfgFileType,
          index: number
        ) => {
          try {
            const newGroupItem = {
              ...groupItem,
              file: groupItem.file.replace('../common/', ''),
              type
            }
            // 1、创建拖拽目标:::一层文件
            const res1 = await engineApi.addCalcs?.(newGroupItem, index)

            // 2、拖拽目标:::插入二层文件内容
            const calcFile =
              newGroupItem.type === CfgFileType.Common
                ? `../common/${newGroupItem.file}`
                : newGroupItem.file
            const res2 = await engineApi.modifyCalc?.(calcFile, groupable, newGroupItem.type)
            // 3、拖拽目标:::save 保存二层文件内容
            const res3 = await engineApi.saveCalc?.(calcFile, newGroupItem.type)
            return res1 && res2 && res3
          } catch (err) {
            console.error('执行中断，错误原因：', err)
            return false
          }
        },
        copyCommonToSpecific: async (
          groupItem: CalsGroupItem,
          dropIndex: number,
          draggingIndex: number
        ) => {
          // 2、被拖拽:::remove 一层文件被拖拽移除老的内容
          const res2 = await engineApi.removeCalcs?.(draggingIndex)
          // 2.0、读取被拖拽:::二层内容
          const groupable = (await engineApi.loadCalc?.(groupItem.file, groupItem.type)) || {}
          const res = await dragCalcApi.baseFn(
            groupItem,
            groupable,
            CfgFileType.EngineSpecific,
            dropIndex
          )
          const res1 = await engineApi.modifyCalcs?.(draggingIndex, {
            ...groupItem,
            file: groupItem.file.replace('../common/', ''),
            type: CfgFileType.EngineSpecific
          })
          return res && res1 && res2
        },
        pasteSpecificToCommon: async (
          groupItem: CalsGroupItem,
          dropIndex: number,
          draggingIndex: number
        ) => {
          try {
            // 2、被拖拽:::remove 一层文件被拖拽移除老的内容
            const res2 = await engineApi.removeCalcs?.(draggingIndex)
            // 2.0、读取被拖拽:::二层内容
            const groupable = (await engineApi.loadCalc?.(groupItem.file, groupItem.type)) || {}
            const res1 = await dragCalcApi.baseFn(
              groupItem,
              groupable,
              CfgFileType.Common,
              dropIndex
            )
            return res1 && res2
          } catch (err) {
            console.error('执行中断，错误原因：', err)
            return false
          }
        }
      }
      return {
        ...engineApi,
        ...dragCalcApi,
        bindHandler: (handler: (...args: any[]) => void) => {
          useHandler(bizEngine, BizEngine[fnPtr.handleEventName], handler)
        }
      }
    }
  }
  return dynamicApi()
}
