{"name": "cfg-builder", "productName": "Builder", "private": true, "version": "0.0.1", "main": "dist/main/index.js", "scripts": {"dev": "cross-env NODE_ENV=development vite", "dev:win32": "cross-env NODE_ENV=development NODE_PLAT=win32 vite", "clean": "rimraf ./dist && rimraf ./output", "build": "rimraf ./dist && vite build && bash ./build/deploy/copy_to_dist.sh", "copy": "rimraf ./dist/cfg && gulp --gulpfile build/gulp.js", "build:test": "cross-env NODE_ENV=test vite build", "build:pkg": "rimraf ./output && electron-builder --config electron-builder.yml", "make": "npm run clean && npm run build && npm run build:pkg"}, "license": "Private", "dependencies": {"@egjs/component": "^2.2.2", "@element-plus/icons-vue": "^2.3.1", "@lezer/generator": "^1.7.3", "@lezer/highlight": "^1.2.1", "@lezer/lr": "^1.4.2", "@moveable/helper": "^0.1.2", "@vueuse/core": "^13.0.0", "@wuk/cfg": "0.0.276", "@wuk/wui": "1.1.107", "ag-grid-vue3": "33.0.2", "aieditor": "^1.2.6", "clsx": "^1.0.4", "codemirror": "6.0.1", "echarts": "^5.6.0", "echarts-gl": "^2.0.9", "framework-utils": "^0.3.4", "html2canvas": "^1.3.2", "keycon": "^1.4.0", "lodash": "^4.17.21", "mavon-editor": "^3.0.2", "moveable-helper": "^0.4.0", "react-dnd-html5-backend": "^16.0.1", "scenejs": "^1.10.3", "vue": "^3.4.31", "vue-codemirror": "6.1.1", "vue-resizable-panels": "^0.0.1", "vue-router": "^4.4.0", "vue-virtual-scroller": "^2.0.0-beta.8", "vue3-dnd": "^2.1.0", "vue3-guides": "^0.12.2", "vue3-infinite-viewer": "^0.17.1", "vue3-moveable": "^0.28.0", "vue3-selecto": "^1.12.3", "vue3-slider": "^1.10.1"}, "devDependencies": {"@types/lodash": "^4.17.20", "@types/node": "18.x", "@types/trusted-types": "^1.0.6", "@types/yauzl": "^2.10.0", "@types/yazl": "^2.4.2", "@typescript-eslint/eslint-plugin": "^5.9.0", "@typescript-eslint/parser": "^5.9.0", "@vitejs/plugin-vue": "^5.2.1", "@vitejs/plugin-vue-jsx": "^4.1.1", "babel-eslint": "^10.1.0", "cross-env": "7.0.3", "electron": "^31.0.0", "electron-builder": "^24.0.0", "eslint": "^8.45.0", "eslint-config-prettier": "^6.15.0", "eslint-plugin-prettier": "^3.1.4", "eslint-plugin-vue": "^9.26.0", "gulp": "^4.0.2", "prettier": "^2.1.2", "rimraf": "^3.0.2", "rollup-plugin-terser": "^7.0.2", "rollup-plugin-visualizer": "^5.10.0", "sass": "1.77.6", "sass-loader": "^13.3.2", "typescript": "^5.0.2", "vite": "^5.4.3", "vite-plugin-chunk-split": "^0.4.7", "vite-plugin-compression": "^0.5.1", "vite-plugin-electron": "^0.15.4", "vite-plugin-electron-renderer": "^0.14.6", "worker-loader": "^3.0.5"}, "eslintIgnore": ["/build", "/output", "/dist"], "eslintConfig": {"globals": {"__VERSION__": true, "__PROP__": true, "__DEV__": true}}, "lint-staged": {"packages/**/*.{js,ts,tsx}": "eslint --fix"}}