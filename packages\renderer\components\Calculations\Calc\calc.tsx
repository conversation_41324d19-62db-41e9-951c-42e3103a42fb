import { NodeDropEvent, TreeContent, TreeNode, TreeProps } from '../../TreeContent'
import { computed, defineComponent, onMounted, PropType, ref, provide, reactive, toRef } from 'vue'
import { SetupEquation, SetupGroup } from './index'
import { CfgFileType, uuid } from '@wuk/cfg'
import {
  CalcMode,
  CalcModeType,
  CalcTree,
  CalcTreeChild,
  CurEditCalcInfo,
  calcContextKey
} from './constants'
import { useBiz } from './hooks'
import { WuiMessage } from '@wuk/wui'
export default defineComponent({
  name: 'Calc',
  props: {
    calcMode: {
      type: String as PropType<CalcModeType>,
      readonly: true,
      required: true
    }
  },
  setup(props) {
    const bizCalcs = useBiz(props.calcMode)
    // const calcData = ref<CalsSignalItem[]>([])
    const treeData = ref<CalcTree[]>([])
    const treeContentRef = ref<InstanceType<typeof TreeContent>>()
    const defaultExpandedKeys = ref<number[]>([])
    // group length
    let groupLen = 0
    // dragging groupName
    const draggingGroupInfo = {
      groupName: '',
      groupId: -1
    }
    /**
     * @type {*}
     * @param {CalcsSignalType | CalcsExcuteType} calcId 一级节点id
     * @param {number} groupNodeIndex 二级节点index
     * */
    const curEditCalcInfo = ref<CurEditCalcInfo>({
      label: '',
      calcId: -1,
      index: 0,
      groupNodeIndex: -1,
      children: []
    })
    /**
     * @description 二级节点 label
     */
    const groupNodeLabel = ref('')
    const mode = ref<'setup-group' | 'setup-equation'>('setup-group')

    const isDragging = computed(() =>
      [CalcMode.Initial, CalcMode.Final].includes(props.calcMode as never)
    )
    const tipsMessage = () => {
      WuiMessage({
        message: 'success',
        type: 'success',
        offset: 90
      })
    }
    const handleNodeChange = (data: CalcTree, node: TreeNode) => {
      const { level, parent } = node
      if (level === 1) {
        mode.value = 'setup-group'
        curEditCalcInfo.value = {
          ...data,
          calcId: data.id,
          groupNodeIndex: -1
        }
      } else if (level === 2) {
        const { data: parentData } = parent
        mode.value = 'setup-equation'
        curEditCalcInfo.value = {
          ...(parentData as CalcTree),
          calcId: parentData.id,
          groupNodeIndex: data.index ?? -1
        }
        groupNodeLabel.value = data.label
      }
    }
    const isGroupMode = computed(() => mode.value === 'setup-group')
    /**
     * @description 获取计算信号
     */
    const getCalcsSignalOptions = async () => {
      if (props.calcMode === CalcMode.Signal) {
        treeData.value = [
          {
            label: 'Calibrate',
            id: CfgFileType.Calibrate,
            index: 0,
            children: []
          }
        ]
      } else if (props.calcMode === CalcMode.Sys_common) {
        treeData.value = [
          {
            label: 'Common',
            id: CfgFileType.Common,
            index: 0,
            children: []
          }
        ]
      } else {
        treeData.value = [
          {
            label: 'Common',
            id: CfgFileType.Common,
            index: 0,
            children: []
          },
          {
            label: 'Specific',
            id: CfgFileType.EngineSpecific,
            index: 1,
            children: []
          }
        ]
      }
      const groupList = (await bizCalcs.readCalcs?.()) || []
      groupList.forEach((item, originIndex) => {
        const idx = item.type === CfgFileType.EngineSpecific ? 1 : 0
        const treeItem = treeData.value[idx]
        const len = treeItem.children?.length || 0
        const id = uuid()
        if (item.group_name === draggingGroupInfo.groupName) {
          draggingGroupInfo.groupId = id
        }
        treeItem.children?.push({
          label: item.group_name,
          id,
          index: len,
          originData: {
            index: originIndex,
            ...item
          }
        })
      })
      const { id, ...args } = treeData.value[curEditCalcInfo.value.index ?? 0]
      curEditCalcInfo.value = {
        calcId: id,
        ...args
      }
      if (draggingGroupInfo.groupId !== -1) {
        changeTreeNode(draggingGroupInfo.groupId)
      }
      groupLen = groupList.length
    }
    /**
     * @description modify calc group
     */
    const changeTreeNode = async (id: number) => {
      treeContentRef.value?.treeRef?.setCurrentKey(id)
    }
    const handleAllowDrag: TreeProps<CalcTree>['allowDrag'] = node => {
      const { level } = node
      if (level === 1) return false
      if (level === 2) return true
      return false
    }
    const handleAllowDrop: TreeProps<CalcTree>['allowDrop'] = (_, dropNode, type) => {
      const { level } = dropNode
      if (level === 1) {
        if (type === 'inner') return true
        return false
      }
      if (level === 2) {
        if (type === 'inner') return false
        return true
      }
      return false
    }
    const handleNodeDrop: NodeDropEvent<CalcTreeChild> = async (draggingNode, dropNode, type) => {
      const {
        id,
        index,
        originData: dropOgData = { type: id, index: groupLen - 1 }
      } = dropNode.data
      const { originData: draggingOgData } = draggingNode.data
      const newIdx =
        type === 'before' || dropNode.level === 1 ? dropOgData.index : dropOgData.index + 1
      let res: boolean | undefined
      const { index: dragIdx, ...groupItem } = draggingOgData
      if (dropOgData.type === CfgFileType.Common) {
        res = await bizCalcs.pasteSpecificToCommon(groupItem, newIdx, dragIdx)
      } else {
        res = await bizCalcs.copyCommonToSpecific(groupItem, newIdx, dragIdx)
      }
      if (!res) return
      curEditCalcInfo.value.index = dropNode.data.originData ? 0 : index
      draggingGroupInfo.groupName = draggingOgData.group_name
      tipsMessage()
    }
    bizCalcs.bindHandler(getCalcsSignalOptions)
    onMounted(async () => {
      await getCalcsSignalOptions()
      const defaultId = treeData.value[0].id
      defaultExpandedKeys.value = [defaultId]
      treeContentRef.value?.treeRef?.setCurrentKey(defaultId)
    })
    provide(
      calcContextKey,
      reactive({
        bizCalcs,
        calcMode: toRef(props.calcMode),
        curEditCalcInfo,
        changeTreeNode
      })
    )
    return () => {
      return (
        <>
          <TreeContent
            ref={treeContentRef}
            onTree-node-change={handleNodeChange}
            treeData={treeData.value}
            treeAreaMenu={[]}
            draggable={isDragging.value}
            showRightMenu={false}
            rightContentPadding={0}
            allowDrag={handleAllowDrag}
            allowDrop={handleAllowDrop}
            onNode-drop={handleNodeDrop}
            v-model:default-expanded-keys={defaultExpandedKeys.value}>
            {{
              header: () =>
                isGroupMode.value ? (
                  <SetupGroup.Header />
                ) : (
                  <SetupEquation.Header label={groupNodeLabel.value} />
                ),
              default: () => (isGroupMode.value ? <SetupGroup.Table /> : <SetupEquation.Table />)
            }}
          </TreeContent>
        </>
      )
    }
  }
})
