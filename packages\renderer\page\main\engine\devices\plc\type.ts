import { BaseTableRow } from '@/renderer/components/TableTool'
import { PlcSignal, PlcCfgOption } from '@wuk/cfg'
import { InjectionKey, Ref } from 'vue'

// 基础参数接口
export interface SignalParam {
  currentPlcIndex: number
  plcDeviceIndex: number
  signalIndex?: number
  addr?: number
  type?: string
}

// 信号范围接口
export interface SignalRange {
  min: number
  max: number
  unit: string
}

// 校准数据类型枚举
export enum CalibDataType {
  None = 0,
  Polynomial = 1,
  Table = 2
}

// 校准数据接口
export interface CalibData {
  type: CalibDataType
  min: number
  max: number
  data: number[]
  raw_type: CalibDataType
}

// 多项式对话框模型接口
export interface PolyDialogModel {
  data: number[]
  min: number
  max: number
  type: CalibDataType
}

// 表格对话框行接口
export type TableDialog = {
  x: number
  y: number
}

export type TableDialogRow = BaseTableRow<TableDialog>

// 表格对话框内部模型接口
export interface TableDialogInternalModel {
  data: TableDialogRow[]
  min: number
  max: number
  type: CalibDataType
}

// 表格对话框输出模型接口
export interface TableDialogModel {
  data: number[]
  min: number
  max: number
  type: CalibDataType
}

// 信号模型接口
export interface SignalModel {
  name: string
  signal_range: SignalRange
  scale_range: SignalRange
  scale_factor: number
  comments_str: string
  comments: string[]
  calib_data: CalibData
}

// 多项式对话框属性接口
export interface PolyDialogProps {
  modelValue: boolean
  calibData: CalibData
  signalData: SignalData
}

export type SignalData = {
  signal: string
  source: number
}

// 表格对话框属性接口
export interface TableDialogProps {
  modelValue: boolean
  calibData: CalibData
  signalData: SignalData
}

// 信号对话框属性接口
export interface SignalDialogProps {
  params: SignalParam
  modelShow: boolean
}

// PLC信号表格行类型
export type PlcSignalTableRow = BaseTableRow<PlcSignal & { meta: PlcSignal; addr: number }>

// 提交信号参数接口
export interface SubmitSignalParams {
  currentPlcIndex: number
  plcDeviceIndex: number
  signalIndex?: number
  signalData?: any
  signalModel?: SignalModel
}

// PLC上下文接口
export interface PLCContext {
  plcList: Ref<PlcCfgOption[]>
  modifyPLCSetup: (plcSetupIndex: number, modifiedData: any) => Promise<boolean>
  submitSignalData: (params: SubmitSignalParams) => Promise<boolean>
}

// PLC上下文注入键
export const plcContextKey: InjectionKey<PLCContext> = Symbol('plcsetpContext')

// 常量定义
export const POLY_MAX_DEGREE = 10
export const DEFAULT_COEFF_VALUE = '0.0000'
