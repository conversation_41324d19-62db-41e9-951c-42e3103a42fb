import { computed, defineComponent, onMounted, PropType, provide, reactive, ref } from 'vue'
import { TreeContent } from '../../TreeContent'
import SetupGroup from './setupGroup'
import SetupEquation from './setupEquation'
import {
  calcTableContextKey,
  CalcTableTree,
  CurTableEqInfo,
  CurTableGroupInfo,
  TableModeType
} from './constants'
import { uuid } from '@wuk/cfg'
import { useHandler } from '@/renderer/hooks'
import { useBiz } from './hooks'
export default defineComponent({
  name: 'CalcTable',
  props: {
    calcTableMode: {
      type: String as PropType<TableModeType>,
      required: true
    }
  },
  setup(props) {
    const treeData = ref<CalcTableTree[]>([])
    const defaultExpandedKeys = ref([0])
    const { bizTable, BizTable } = useBiz(props.calcTableMode)
    const treeContentRef = ref<InstanceType<typeof TreeContent>>()
    const mode = ref<'setup-group' | 'setup-equation'>('setup-group')
    const isGroupMode = computed(() => mode.value === 'setup-group')
    const curTableGroupInfo = ref<CurTableGroupInfo>({
      groupName: '',
      label: '',
      calcTableId: -1,
      groupNodeIndex: -1
    })
    const curTableEqInfo = ref<CurTableEqInfo>({
      item: {} as CurTableEqInfo['item'],
      tableIndex: -1
    })
    /**
     * @description 获取计算信号
     */
    const getCalcTableOptions = async () => {
      treeData.value = [
        {
          label: props.calcTableMode,
          id: 0,
          index: 0,
          children: []
        }
      ]
      const { groups = [] } = (await bizTable.value?.readTableOptions()) || {}
      treeData.value[0].children = groups.map((item, index) => ({
        label: item.name,
        id: uuid(),
        index,
        originData: {
          group_name: item.name,
          tables: item.tables
        }
      }))
      if (curTableGroupInfo.value.groupNodeIndex !== -1) {
        changeTreeNode(curTableGroupInfo.value.groupNodeIndex)
      }
    }
    const handleNodeChange = (data: CalcTableTree, node: any) => {
      const { level, parent } = node
      if (level === 1) {
        mode.value = 'setup-group'
        curTableGroupInfo.value = {
          ...data,
          groupName: '',
          calcTableId: data.id,
          groupNodeIndex: -1
        }
      } else if (level === 2) {
        const { data: parentData } = parent
        mode.value = 'setup-equation'
        curTableGroupInfo.value = {
          ...parentData,
          groupName: data.label,
          calcTableId: parentData.id,
          groupNodeIndex: data.index ?? -1
        }
      }
    }
    /**
     * @description modify calc table group
     */
    const changeTreeNode = async (id: number) => {
      treeContentRef.value?.treeRef?.setCurrentKey(id)
    }
    useHandler(bizTable, BizTable.value.onTablesOptionsChanged, getCalcTableOptions)
    onMounted(async () => {
      await getCalcTableOptions()
      const { id, ...args } = treeData.value[0]
      curTableGroupInfo.value = {
        calcTableId: id,
        groupNodeIndex: -1,
        groupName: '',
        ...args
      }
      treeContentRef.value?.treeRef?.setCurrentKey(0)
    })
    provide(
      calcTableContextKey,
      reactive({
        curTableGroupInfo,
        curTableEqInfo,
        changeTreeNode,
        bizTable,
        BizTable
      })
    )
    return () => (
      <>
        <TreeContent
          ref={treeContentRef}
          onTree-node-change={handleNodeChange}
          treeData={treeData.value}
          treeAreaMenu={[]}
          showRightMenu={false}
          rightContentPadding={0}
          v-model:default-expanded-keys={defaultExpandedKeys.value}>
          {{
            header: () => (
              <>
                {isGroupMode.value ? (
                  <SetupGroup.Header />
                ) : (
                  <SetupEquation.Header label={curTableGroupInfo.value.groupName} />
                )}
              </>
            ),
            default: () => <>{isGroupMode.value ? <SetupGroup.Table /> : <SetupEquation.Table />}</>
          }}
        </TreeContent>
      </>
    )
  }
})
