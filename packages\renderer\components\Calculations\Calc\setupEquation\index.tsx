import {
  defineComponent,
  inject,
  reactive,
  type FunctionalComponent,
  computed,
  ref,
  watch
} from 'vue'
import { usePermanentFocus } from '@/renderer/hooks'
import { calcContextKey } from '../constants'
import { useBem } from '@/renderer/hooks/bem'
import $styles from './index.module.scss'
import { WuiForm, WuiMessage, WuiSelect } from '@wuk/wui'
import { EquationHeader } from '../../CalcTool'
import { CalcGroupable, CalcWhenToExcuteType } from '@wuk/cfg'
import { cloneFnJSON } from '@vueuse/core'
import {
  EquationHeaderProps,
  EquationTableRow,
  ModelType,
  EditRowTableExpose,
  EditRowCodeExpose
} from './type'
import { calcEqRules } from '../hooks'
import EditRowTable from './editRowTable'
import EditRowCode from './editRowCode'
import { nextTick } from 'vue'
import { EditModeTool, EditMode } from '../../CalcTool'
import FunctionsButton from '@/renderer/components/FunctionsButton/index.vue'
const { b, e } = useBem('setup-equation', $styles)
const { handleInputClick, handleSelect } = usePermanentFocus<EquationTableRow>()
const insertValue = ref('')
const whenToExcuteOptions = [
  {
    label: 'None',
    value: CalcWhenToExcuteType.kNone
  },
  {
    label: 'Select Engine',
    value: CalcWhenToExcuteType.kSelectEngine
  },
  {
    label: 'Init Displays',
    value: CalcWhenToExcuteType.kInitDisplays
  },
  {
    label: 'Open Test',
    value: CalcWhenToExcuteType.kOpenTest
  },
  {
    label: 'Load Tables',
    value: CalcWhenToExcuteType.kLoadTables
  },
  {
    label: 'All Times',
    value: CalcWhenToExcuteType.kAllTimes
  }
]
const Table = defineComponent({
  props: {},
  setup() {
    const calcContext = inject(calcContextKey)
    if (!calcContext) return
    const permanentFocus = ref(true)
    const excSelRef = ref<InstanceType<typeof WuiSelect>>()
    const eqFormRef = ref<InstanceType<typeof WuiForm>>()
    const editRowTableRef = ref<EditRowTableExpose>()
    const editRowCodeRef = ref<EditRowCodeExpose>()
    const tipsMessage = () => {
      WuiMessage({
        message: 'success',
        type: 'success',
        offset: 90,
        grouping: true
      })
    }
    const model = reactive<ModelType>({
      when_to_excute: CalcWhenToExcuteType.kSelectEngine,
      editMode: EditMode.Table,
      list: []
    })
    let originCalcGroupable: CalcGroupable | undefined

    const handleModifyCalc = async () => {
      const { bizCalcs, curEditCalcInfo } = calcContext
      const { children = [], groupNodeIndex = -1 } = curEditCalcInfo
      if (groupNodeIndex === -1) return
      const { label, originData } = children[groupNodeIndex]
      const { name, comments = [] } = originCalcGroupable || {}
      const newLines = model.list.map(item => {
        const { row_type, flag, ...lines } = item
        return lines
      })
      return await bizCalcs.modifyCalc?.(
        originData.file,
        cloneFnJSON({
          name: name || label,
          comments,
          excute: model.when_to_excute,
          lines: newLines
        }),
        originData.type
      )
    }

    const handleChange = async () => {
      const res = await handleModifyCalc()
      excSelRef.value?.blur()
      permanentFocus.value = true
      editRowCodeRef.value?.code.codeFocus()
      if (!res) return
      tipsMessage()
    }

    /**
     * @description save option
     */
    const handleSave = async () => {
      if (model.editMode === EditMode.Table) {
        await editRowTableRef.value?.handleSave()
      } else {
        await editRowCodeRef.value?.handleSave()
      }
    }

    watch(insertValue, val => {
      if (!val) return
      editRowCodeRef.value?.code.insert(val)
      insertValue.value = ''
    })

    watch(
      () => model.editMode,
      editMode => {
        permanentFocus.value = true
      }
    )

    return () => (
      <wui-form
        ref={eqFormRef}
        label-width='0'
        label-position='left'
        validate-box-style='fill'
        hide-required-asterisk
        inline-message
        validate-box-gap='3'
        validate-placement='bottom'
        model={model}
        rules={calcEqRules}
        class={[b(), 'cfg-setup']}>
        <wui-form-item label-width='180' align='center'>
          {{
            label: () => <div class={e('label')}>When to Execute</div>,
            default: () => (
              <wui-select
                ref={excSelRef}
                v-model={model.when_to_excute}
                onChange={handleChange}
                onClick={() => {
                  permanentFocus.value = false
                  nextTick(() => {
                    excSelRef.value!.dropdownMenuVisible = true
                    excSelRef.value?.focus()
                  })
                }}
                placeholder='Please input when to excute'>
                {whenToExcuteOptions.map(option => (
                  <wui-option key={option.value} label={option.label} value={option.value} />
                ))}
              </wui-select>
            )
          }}
        </wui-form-item>
        <wui-form-item label-width='180'>
          {{
            label: () => <div class={e('label')}>Edit Mode</div>,
            default: () => <EditModeTool v-model:editMode={model.editMode} />
          }}
        </wui-form-item>
        <div class='cfg-setup_table'>
          {model.editMode === EditMode.Table ? (
            <EditRowTable
              ref={editRowTableRef}
              v-model:model={model}
              eqFormRef={eqFormRef.value}
              onInput-click={handleInputClick}
              handleModifyCalc={handleModifyCalc}
            />
          ) : (
            <EditRowCode v-model:permanentFocus={permanentFocus.value} ref={editRowCodeRef} />
          )}
        </div>
        <div class={['cfg-submit', e('footer')]}>
          <wui-button type='primary' onClick={handleSave}>
            Ok
          </wui-button>
        </div>
      </wui-form>
    )
  }
})

const Header: FunctionalComponent<EquationHeaderProps> = props => {
  return (
    <EquationHeader
      onSelect={(val: string) => {
        insertValue.value = val
        handleSelect(val)
      }}>
      {{
        default: () => <div>Calculation Group: {props.label}</div>,
        rt_content: () => <FunctionsButton onSelect={handleSelect} />
      }}
    </EquationHeader>
  )
}
Header.props = {
  label: {
    type: String,
    default: ''
  }
}

const SetupEquation = {
  Table,
  Header
}
export default SetupEquation
