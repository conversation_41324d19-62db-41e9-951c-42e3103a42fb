import {
  computed,
  defineComponent,
  inject,
  reactive,
  toRef,
  watchEffect,
  type FunctionalComponent
} from 'vue'
import { calcContextKey, type CalcTreeChild, type CalcTree } from '../constants'
import { uuid } from '@wuk/cfg'
import { WuiForm, WuiMessage } from '@wuk/wui'
import TableTool, { BaseTableRow, isAddOrInsertType, type OpType } from '../../../TableTool'
import { useTableCommonMenu } from '@/renderer/hooks'
import { useCalcGpRules } from '../hooks'
import { ref } from 'vue'
type TableRow = BaseTableRow<
  CalcTreeChild['originData'] & {
    id: CalcTree['id']
  }
>

const Table = defineComponent({
  props: {},
  setup() {
    const calcContext = inject(calcContextKey)
    const gpFormRef = ref<InstanceType<typeof WuiForm>>()
    const model = reactive({
      list: [] as TableRow[]
    })
    const { calcGpRules } = useCalcGpRules()
    const calcChildren = computed(() => calcContext?.curEditCalcInfo.children || [])
    const { handleRowMenu, handleTableAreaCtxMenu } = useTableCommonMenu(
      toRef(model, 'list'),
      async (key, ...args) => {
        const { rowIndex, row } = args[0]
        switch (key) {
          case 'addKey':
            model.list.push(createRow('add', true, row ? row.index + 1 : 0))
            break
          case 'insertKey':
            model.list.splice(rowIndex + 1, 0, createRow('insert', true, row.index + 1))
            break
          case 'modifyKey':
            handleOp('edit', row, rowIndex)
            break
          case 'deleteKey':
            handleOp('delete', row, rowIndex)
            break
          case 'equationKey':
            calcContext?.changeTreeNode(row.id)
            break
        }
      },
      [1],
      [
        {
          key: 'addKey',
          label: 'add'
        },
        {
          key: 'insertKey',
          label: 'insert'
        },
        {
          key: 'modifyKey',
          label: 'modify'
        },
        {
          key: 'deleteKey',
          label: 'delete'
        },
        {
          key: 'equationKey',
          label: 'equation Editor'
        }
      ]
    )
    const tipsMessage = () => {
      WuiMessage({
        message: 'success',
        type: 'success',
        offset: 90,
        grouping: true
      })
    }
    function createRow(
      row_type: TableRow['row_type'],
      flag: boolean,
      insertIndex: number
    ): TableRow {
      const { calcId = -1 } = calcContext?.curEditCalcInfo || {}
      return {
        flag,
        group_name: '',
        file: '',
        index: insertIndex,
        type: calcId,
        row_type,
        id: uuid()
      }
    }
    /**
     * @description handle op cancel
     */
    const handleOpCancel = (row: TableRow, index?: number) => {
      if (isAddOrInsertType(row.row_type)) {
        model.list.splice(index!, 1)
        return
      }
      const { group_name } = calcChildren.value[index!].originData
      row.group_name = group_name
      row.flag = false
    }
    const handleOp = (op: OpType, row: TableRow, index: number) => {
      switch (op) {
        case 'edit':
          row.flag = true
          break
        case 'delete':
          handleDelete(row.index, index)
          break
        case 'cancel':
          handleOpCancel(row, index)
          break
        case 'select':
          handleSelect(row, index)
          break
      }
    }
    /**
     * @description handle op save
     */
    async function handleSelect(row: TableRow, index: number) {
      if (!calcContext) return
      const valid = await gpFormRef.value?.validateField([`list.${index}.group_name`])
      if (!valid) return
      const { flag, row_type, id, ...params } = row
      params.file = params.file || `${params.group_name}.cal`
      let res: boolean | undefined
      if (isAddOrInsertType(row.row_type)) {
        res = await calcContext.bizCalcs.addCalcs?.(params, row.index)
      } else {
        res = await calcContext.bizCalcs.modifyCalcs?.(row.index, params)
      }
      if (!res) return
      row.flag = false
      row.row_type = '*'
      tipsMessage()
    }
    /**
     * @description handle op delete
     */
    async function handleDelete(removeIndex: number, index: number) {
      if (!calcContext) return
      const res = await calcContext.bizCalcs.removeCalcs?.(removeIndex)
      if (!res) return
      model.list.splice(index, 1)
      tipsMessage()
    }

    watchEffect(() => {
      const { children = [] } = calcContext?.curEditCalcInfo || {}
      console.log(children, 'childrenchildrenchildrenchildren')
      model.list = children.map(item => ({
        ...item.originData,
        flag: false,
        row_type: '*',
        id: item.id
      }))
    })
    return () => (
      <wui-form
        ref={gpFormRef}
        label-width='0'
        label-position='left'
        validate-box-style='fill'
        hide-required-asterisk
        inline-message
        validate-box-gap='3'
        validate-placement='bottom'
        model={model}
        rules={calcGpRules}
        class={['cfg-setup']}>
        <div class='cfg-setup_table'>
          <wui-table data={model.list} height='100%' border onRowContextmenu={handleRowMenu}>
            {{
              default: () => (
                <>
                  <wui-table-column label='No.' type='index' width='80px' align='center' />
                  <wui-table-column prop='group_name' label='Group Name' align='center'>
                    {{
                      default: ({ row, $index }: any) => (
                        <>
                          {row.flag ? (
                            <wui-form-item
                              prop={`list.${$index}.group_name`}
                              rules={calcGpRules.group_name}>
                              <wui-input
                                v-model={row.group_name}
                                type='text'
                                placeholder='Group Name'
                              />
                            </wui-form-item>
                          ) : (
                            <div>{row.group_name}</div>
                          )}
                        </>
                      )
                    }}
                  </wui-table-column>
                  <wui-table-column label='Op' width='100px' align='center'>
                    {{
                      default: ({ row, $index }: any) => (
                        <TableTool.Op flag={row.flag} onOp={op => handleOp(op, row, $index)} />
                      )
                    }}
                  </wui-table-column>
                </>
              ),
              empty: () => <TableTool.Empty onCustom-contextmenu={handleTableAreaCtxMenu} />
            }}
          </wui-table>
        </div>
      </wui-form>
    )
  }
})

const Header: FunctionalComponent = () => <div>Calculation Groups</div>

const SetupGroup = {
  Header,
  Table
}
export default SetupGroup
