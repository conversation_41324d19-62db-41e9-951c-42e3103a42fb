$namespace: 'cfg';
html,
body {
  width: 100% !important;
  height: 100%;
  margin: 0;
  overflow: hidden;
  font-family: 'PingFang SC', 'Microsoft YaHei', '微软雅黑', Arial, sans-serif;
}

#root {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.titlebar {
  display: flex;
  align-items: center;
  flex-shrink: 0;
  height: 35px;
  width: 100%;
  z-index: 9999;

  padding-left: 12px;
  font-size: 14px;
}

.content {
  overflow: auto;
}
@mixin cfg-button {
  .wui-button {
    width: 66px;
    height: 32px;
  }
  .wui-button--primary {
    --wui-button-bg-color: #6282c1;
    --wui-button-border-color: #3c5992;
    --wui-button-active-bg-color: #6282c1;
    --wui-button-active-border-color: #3c5992;
    --wui-button-hover-border-color: #3c5992;
    --wui-button-hover-bg-color: rgba(98, 130, 193, 0.7);
  }
  .wui-button--info.is-plain {
    width: 83px;
    height: 32px;
    --wui-button-bg-color: #f0f0f0;
    --wui-button-border-color: #b6bece;
    --wui-button-text-color: #3d3d3d;
  }
}
.#{$namespace} {
  &-submit {
    margin-top: 20px;
    text-align: right;
    @include cfg-button;
  }
  &-btn-box {
    @include cfg-button;
  }
  &-setup {
    display: flex;
    flex-direction: column;
    height: 100%;
    &_table {
      // overflow: auto;
      flex: 1;
      // 关键：初始大小为剩余空间，而不是向外扩展
      flex-basis: auto;
      height: 0;
      .wui-form-item {
        margin-bottom: 0;
      }
      .wui-input {
        width: 100%;
        height: 32px;
      }
      .wui-select {
        width: 100%;
        height: 32px;
        margin-left: 0;
      }
      .wui-table {
        thead th {
          &.wui-table__cell {
            background: #EAF1FD;
            color: #90AFE4;
            font-weight: bold;
            font-size: 18px;
          }
        }
      }
    }
  }
}

// 单行注释
.cfg-sle {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}


