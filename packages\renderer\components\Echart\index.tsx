import { defineComponent, useModel, watch, onBeforeUnmount, onMounted, nextTick } from 'vue'
import $styles from './index.module.scss'
import { useBem } from '@/renderer/hooks'
import _ from 'lodash'
import 'echarts-gl'

import * as echarts from 'echarts/core'
import { Bar<PERSON>hart, LineChart } from 'echarts/charts'
import {
  TitleComponent,
  TooltipComponent,
  GridComponent,
  DatasetComponent,
  TransformComponent,
  LegendComponent,
  DataZoomComponent,
  VisualMapComponent
} from 'echarts/components'
import { LabelLayout, UniversalTransition } from 'echarts/features'
import { CanvasRenderer } from 'echarts/renderers'
import type { BarSeriesOption, LineSeriesOption } from 'echarts/charts'
import type {
  TitleComponentOption,
  TooltipComponentOption,
  GridComponentOption,
  DatasetComponentOption,
  LegendComponentOption,
  DataZoomComponentOption,
  VisualMapComponentOption
} from 'echarts/components'
import type { ComposeOption } from 'echarts/core'

export type ECOption = ComposeOption<
  | BarSeriesOption
  | LineSeriesOption
  | TitleComponentOption
  | TooltipComponentOption
  | GridComponentOption
  | DatasetComponentOption
  | LegendComponentOption
  | DataZoomComponentOption
  | VisualMapComponentOption
>

echarts.use([
  TitleComponent,
  TooltipComponent,
  GridComponent,
  DatasetComponent,
  TransformComponent,
  BarChart,
  LineChart,
  LabelLayout,
  UniversalTransition,
  CanvasRenderer,
  LegendComponent,
  DataZoomComponent,
  VisualMapComponent
])

import { uuid } from '@wuk/cfg'

import { defaultBarConfig, defaultLineConfig, defaultLine3DConfig } from './process'

export default defineComponent({
  name: 'Echart',
  props: {
    option: {
      type: Object as () => ECOption,
      default: () => {}
    }
  },
  setup(props) {
    //------------ref----------
    const id = `chart_${uuid()}`
    const opt = useModel(props, 'option')
    const { e } = useBem('echart', $styles)
    let myChart: echarts.ECharts | null = null
    const typeMapFun: Record<string, Function> = {
      line: defaultLineConfig,
      bar: defaultBarConfig,
      line3D: defaultLine3DConfig
    }

    //------------methods----------
    const updateChart = () => {
      if (myChart) {
        // 获取所有可能的默认配置
        const baseOption2D = typeMapFun['line']()
        const baseOption3D = typeMapFun['line3D']()

        if (opt.value?.series) {
          const seriesArray = Array.isArray(opt.value.series)
            ? opt.value.series
            : [opt.value.series]

          const mergedSeries = seriesArray.map((customSeries: any) => {
            const seriesType = customSeries.type || 'line'
            // 根据类型选择对应的默认配置
            const defaultSeries = typeMapFun[seriesType]?.().series?.[0] || {}
            return _.merge({}, defaultSeries, customSeries)
          })

          // 检查是否有3D系列，决定使用哪个基础配置
          const has3DSeries = seriesArray.some(series => series.type === 'line3D')
          const baseOption = has3DSeries ? baseOption3D : baseOption2D

          const finalOption = _.merge({}, baseOption, {
            ...opt.value,
            series: mergedSeries
          })
          console.log(finalOption, 2222222)
          myChart.setOption(finalOption)
        } else {
          // 如果没有series，默认使用2D配置
          myChart.setOption(_.merge({}, baseOption2D, opt.value))
        }
        myChart.resize()
      }
    }

    const init = () => {
      myChart = echarts.init(document.getElementById(id))
      updateChart()
    }

    watch(
      () => opt.value,
      () => {
        updateChart()
      },
      { deep: true }
    )

    //------------lifecycle----------
    onMounted(async () => {
      await nextTick()
      init()
    })

    onBeforeUnmount(() => {
      if (myChart) {
        myChart.dispose()
        myChart = null
      }
    })

    //------------render----------
    return () => {
      return (
        <>
          <div id={id} class={e('echart')}></div>
        </>
      )
    }
  }
})
