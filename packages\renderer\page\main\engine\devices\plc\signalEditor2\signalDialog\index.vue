<template>
  <MyDialog v-model="isActive" title="PLC Digital Signal Dialog" @ok="handleSubmit">
    <div :class="e('body')">
      <wui-form
        ref="signalFormRef"
        hide-required-asterisk
        :model="signalModel"
        label-width="120px"
        :class="e('form')"
      >
        <wui-form-item label="Source">
          <span>Channel {{ props.params.addr + ' ' + props.params.type }}</span>
        </wui-form-item>
        <wui-form-item label="Signal Range">
          <wui-input
            v-model="signalModel.signal_range.min"
            placeholder="Minimum"
            style="width: 100px"
          />
          <wui-input
            v-model="signalModel.signal_range.max"
            placeholder="Maximum"
            style="width: 100px; margin-left: 8px"
          />
          <wui-input
            v-model="signalModel.signal_range.unit"
            placeholder="Units"
            style="width: 100px; margin-left: 8px"
          />
        </wui-form-item>
        <wui-form-item label="Scale Range">
          <wui-input
            v-model="signalModel.scale_range.min"
            placeholder="Minimum"
            style="width: 100px"
          />
          <wui-input
            v-model="signalModel.scale_range.max"
            placeholder="Maximum"
            style="width: 100px; margin-left: 8px"
          />
          <wui-input
            v-model="signalModel.scale_range.unit"
            placeholder="Units"
            style="width: 100px; margin-left: 8px"
          />
        </wui-form-item>
        <wui-form-item label="Scale Factor">
          <wui-input
            v-model="signalModel.scale_factor"
            placeholder="Scale Factor"
            style="width: 240px"
          />
        </wui-form-item>
        <wui-form-item label="PLC Calculation">
          <wui-select v-model="signalModel.calib_data.type" style="width: 240px">
            <wui-option :value="0" label="None" />
            <wui-option :value="1" label="Polynomial" />
            <wui-option :value="2" label="Table" />
          </wui-select>
          <wui-button style="margin-top: 8px" @click="handleCalculation"
            >Edit PLC Calculations</wui-button
          >
        </wui-form-item>
        <wui-form-item label="Comments">
          <wui-input
            v-model="signalModel.comments_str"
            type="textarea"
            :rows="5"
            style="width: 100%; padding: 5px"
          />
        </wui-form-item>
      </wui-form>
    </div>
  </MyDialog>
  <PolyDialog
    v-if="polyDialogVisible"
    v-model="polyDialogVisible"
    :calib-data="signalModel.calib_data"
    :signal-data="signalData"
    @save="handleCalibDataSave"
  />
  <TableDialog
    v-if="tableDialogVisible"
    v-model="tableDialogVisible"
    :calib-data="signalModel.calib_data"
    :signal-data="signalData"
    @save="handleCalibDataSave"
  />
</template>

<script lang="ts" setup>
import { reactive, ref, inject, onMounted, watch } from 'vue'
import MyDialog from '@/renderer/components/dialog/index.vue'
import $styles from '../index.module.scss'
import PolyDialog from '../polyDialog/index.vue'
import TableDialog from '../tableDialog/index.vue'
import { useBem } from '@/renderer/hooks/bem'
import { useVModel } from '@vueuse/core'
import {
  plcContextKey,
  SignalDialogProps,
  SignalModel,
  CalibDataType,
  SignalData
} from '../../type'

const { e } = useBem('plc-signal-signal', $styles)

const props = withDefaults(defineProps<SignalDialogProps>(), {
  params: () => ({
    currentPlcIndex: 0,
    plcDeviceIndex: 0,
    signalIndex: 0,
    type: 'ADC'
  }),
  modelShow: true
})

const emit = defineEmits<{
  'update:modelShow': [value: boolean]
}>()

const plcContext = inject(plcContextKey)
const isActive = useVModel(props, 'modelShow', emit)

const polyDialogVisible = ref(false)
const tableDialogVisible = ref(false)

const signalData = reactive<SignalData>({ signal: '', source: 0 })

const signalModel = reactive<SignalModel>({
  name: '',
  signal_range: { min: 0, max: 0, unit: '' },
  scale_range: { min: 0, max: 0, unit: '' },
  scale_factor: 0,
  comments_str: '',
  comments: [''],
  calib_data: {
    type: CalibDataType.None,
    min: 0,
    max: 0,
    data: [0],
    raw_type: CalibDataType.None
  }
})

const handleSubmit = async (): Promise<void> => {
  if (!plcContext?.submitSignalData) return

  signalModel.comments = signalModel.comments_str
    .split('\n')
    .filter(comment => comment.trim() !== '')

  await plcContext.submitSignalData({
    currentPlcIndex: props.params.currentPlcIndex,
    plcDeviceIndex: props.params.plcDeviceIndex,
    signalIndex: props.params.signalIndex,
    signalModel
  })

  isActive.value = false
}

const handleCalculation = (): void => {
  const { type } = signalModel.calib_data
  if (type === CalibDataType.Polynomial) {
    polyDialogVisible.value = true
  } else if (type === CalibDataType.Table) {
    tableDialogVisible.value = true
  }
}

const initializeSignalData = async (): Promise<void> => {
  const signalInfo =
    plcContext?.plcList.value[props.params.currentPlcIndex]?.devices?.[props.params.plcDeviceIndex]
      ?.signals.list[props.params.signalIndex || 0]

  if (!signalInfo) return

  signalModel.name = signalInfo.name || ''

  if (signalInfo.scale_range) {
    Object.assign(signalModel.scale_range, signalInfo.scale_range)
  }

  if (signalInfo.signal_range) {
    Object.assign(signalModel.signal_range, signalInfo.signal_range)
  }

  signalModel.scale_factor = signalInfo.scale_factor || 0
  signalModel.comments = signalInfo.comments || ['']
  signalModel.comments_str = (signalInfo.comments || ['']).join('\n')

  if (signalInfo.calib_data) {
    Object.assign(signalModel.calib_data, {
      ...signalInfo.calib_data,
      raw_type: signalInfo.calib_data.type || CalibDataType.None
    })
  }

  signalData.source = props.params.addr || 0
  signalData.signal = signalModel.name || ''
}

const handleCalibDataSave = (data: any): void => {
  signalModel.calib_data = {
    ...data,
    raw_type: data.type || CalibDataType.None
  }
  handleSubmit()
}

onMounted(() => {
  initializeSignalData()
})
</script>
