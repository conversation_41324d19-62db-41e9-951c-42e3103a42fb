<template>
  <div :class="[b(), 'cfg-setup']">
    <div :class="e('header')">
      <OperatorsButton :class="e('header', 'operator')" @select="handleSelect" />
      <wui-button type="primary" @click="openModel">Parameters</wui-button>
    </div>
    <div :class="[e('content'), 'cfg-setup_table']">
      <wui-table
        show-overflow-tooltip
        border
        height="100%"
        :data="model.list"
        @row-dblclick="handleRowDblClick"
        @row-contextmenu="handleRowMenu"
      >
        <wui-table-column label="Name" min-width="150px" align="center" show-overflow-tooltip>
          <template #default="{ row }">
            <wui-input v-if="row.flag" v-model="row.name" clearable placeholder="Please input" />
            <span v-else>{{ row.name }}</span>
          </template>
        </wui-table-column>
        <wui-table-column label="exp" min-width="200px" align="center" show-overflow-tooltip>
          <template #default="{ row }">
            <wui-input
              v-if="row.flag"
              v-model="row.exp"
              clearable
              placeholder="Please input equation"
              @click="handleInputClick(row, 'exp', $event)"
            />
            <span v-else>{{ row.exp }}</span>
          </template>
        </wui-table-column>
        <wui-table-column label="Op" fixed="right" width="100px" align="center">
          <template #default="{ row, $index }">
            <TableTool.Op :flag="row.flag" @op="handleOp($event, row, $index)" />
          </template>
        </wui-table-column>
        <template #empty>
          <TableTool.Empty @custom-contextmenu="handleTableAreaCtxMenu" />
        </template>
      </wui-table>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, inject, watchEffect, reactive, toRef } from 'vue'
import { useTableCommonMenu } from '@/renderer/hooks'
import { SetupOptions } from '@wuk/cfg'
import { WuiMessage } from '@wuk/wui'
import $styles from './index.module.scss'
import { useBem, usePermanentFocus } from '@/renderer/hooks'
import { useParameterDialog } from '@/renderer/utils/common'

import OperatorsButton from '@/renderer/components/OperatorsButton/index.vue'

import TableTool, {
  OpType,
  isAddOrInsertType,
  RowType,
  BaseTableRow
} from '@/renderer/components/TableTool'
import { contextKey } from '../consts'

type TableRow = BaseTableRow<SetupOptions>

const { e, b } = useBem('vibrationConfigTable', $styles)

const { handleInputClick, handleSelect } = usePermanentFocus<TableRow>()

const vibContext = inject(contextKey)

const model = reactive({
  list: [] as TableRow[]
})

const emits = defineEmits(['dbClick'])

const createRow = (row_type: RowType): TableRow => ({
  name: '',
  exp: '',
  flag: true,
  row_type
})

const openModel = async () => {
  const result = await useParameterDialog()
  handleSelect(result)
}

const tipsMessage = () => {
  WuiMessage({
    message: 'success',
    type: 'success',
    offset: 70
  })
}
// 使用标准的表格菜单钩子
const { handleRowMenu, handleTableAreaCtxMenu } = useTableCommonMenu(
  toRef(model, 'list'),
  async (key, ...args) => {
    const { row, rowIndex } = args[0]
    switch (key) {
      case 'addKey':
        const newRow = createRow('add')
        model.list.push(newRow)
        break
      case 'insertKey':
        const insertRow = createRow('insert')
        model.list.splice(rowIndex + 1, 0, insertRow)
        break
      case 'modifyKey':
        handleOp('edit', row, rowIndex)
        break
      case 'deleteKey':
        handleOp('delete', row, rowIndex)
        break
      default:
        break
    }
  },
  [1],
  [
    { key: 'addKey', label: 'add' },
    { key: 'insertKey', label: 'insert' },
    { key: 'modifyKey', label: 'modify' },
    { key: 'deleteKey', label: 'delete' }
  ]
)

// 操作处理函数
const handleOp = async (op: OpType, row: TableRow, index: number) => {
  switch (op) {
    case 'edit':
      if (row.flag) return
      row.flag = true
      break
    case 'delete':
      const removeResult = await vibContext?.devicePtr?.removeSetup(index)
      if (!removeResult) return
      model.list.splice(index, 1)
      tipsMessage()
      break
    case 'select':
      await onConfirm(row, index)
      break
    case 'cancel':
      onClose(row, index)
      break
  }
}
// 关闭事件
const onClose = (item: TableRow, index: number) => {
  const { row_type, name } = item
  if (isAddOrInsertType(row_type)) {
    model.list.splice(index, 1)
  } else {
    item.name = name
    item.flag = false
  }
}
// 提交事件
const onConfirm = async (item: TableRow, index: number) => {
  const { name, exp, row_type } = item
  if (!name) {
    WuiMessage({
      message: 'Data cannot be empty',
      type: 'warning',
      offset: 80
    })
    return
  }
  const data = { name, exp }
  let editResult
  if (isAddOrInsertType(row_type)) {
    editResult = await vibContext?.devicePtr?.addSetup(
      data,
      row_type === 'insert' ? index : undefined
    )
  } else {
    editResult = await vibContext?.devicePtr?.modifySetup(index, data)
  }
  if (!editResult) return
  item.row_type = '*'
  item.flag = false
  tipsMessage()
}

const handleRowDblClick = (row: any) => {
  emits('dbClick', row)
}

watchEffect(() => {
  const { children = [] } = vibContext?.curEditVibInfo || {}
  model.list = children.map((item: any) => ({
    ...item.originData,
    flag: false,
    row_type: '*',
    id: item.id,
    index: item.index
  }))
})
</script>
